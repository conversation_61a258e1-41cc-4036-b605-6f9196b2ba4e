import unittest
import json
from datetime import datetime
from unittest.mock import patch, MagicMock
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, query_history, add_to_history


class TestQueryHistory(unittest.TestCase):
    """Test cases for the query history functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.app = app.test_client()
        self.app.testing = True
        # Clear query history before each test
        query_history.clear()
    
    def tearDown(self):
        """Clean up after each test method."""
        query_history.clear()
    
    def test_add_to_history_basic(self):
        """Test basic functionality of add_to_history function."""
        nl_query = "Show me all customers"
        sql_query = "SELECT * FROM Customers;"
        
        add_to_history(nl_query, sql_query)
        
        self.assertEqual(len(query_history), 1)
        self.assertEqual(query_history[0]['natural_language_query'], nl_query)
        self.assertEqual(query_history[0]['sql_query'], sql_query)
        self.assertIn('timestamp', query_history[0])
        
        # Verify timestamp is a valid ISO format
        timestamp = query_history[0]['timestamp']
        datetime.fromisoformat(timestamp)  # Should not raise an exception
    
    def test_add_to_history_limit_enforcement(self):
        """Test that history is limited to 5 entries."""
        # Add 7 queries to test the limit
        for i in range(7):
            add_to_history(f"Query {i}", f"SELECT {i};")
        
        # Should only have 5 entries (last 5)
        self.assertEqual(len(query_history), 5)
        
        # Verify the first entry is "Query 2" (oldest of the remaining 5)
        self.assertEqual(query_history[0]['natural_language_query'], "Query 2")
        
        # Verify the last entry is "Query 6" (most recent)
        self.assertEqual(query_history[4]['natural_language_query'], "Query 6")
    
    def test_query_history_endpoint_empty(self):
        """Test query history endpoint when history is empty."""
        response = self.app.get('/query_history')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['history'], [])
    
    def test_query_history_endpoint_with_data(self):
        """Test query history endpoint with data."""
        # Add some test data
        add_to_history("Test query 1", "SELECT 1;")
        add_to_history("Test query 2", "SELECT 2;")
        
        response = self.app.get('/query_history')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        
        # Should return 2 entries in reverse order (most recent first)
        self.assertEqual(len(data['history']), 2)
        self.assertEqual(data['history'][0]['natural_language_query'], "Test query 2")
        self.assertEqual(data['history'][1]['natural_language_query'], "Test query 1")
    
    @patch('app.SQL_Converter')
    def test_convert_to_sql_adds_to_history(self, mock_sql_converter):
        """Test that convert_to_sql endpoint adds queries to history."""
        # Mock the SQL converter
        mock_instance = MagicMock()
        mock_instance.convert_to_sql.return_value = "SELECT * FROM test;"
        mock_sql_converter.return_value = mock_instance
        
        # Make a request to convert_to_sql
        response = self.app.post('/convert_to_sql', data={
            'nl_query': 'Show me test data'
        })
        
        self.assertEqual(response.status_code, 200)
        
        # Verify query was added to history
        self.assertEqual(len(query_history), 1)
        self.assertEqual(query_history[0]['natural_language_query'], 'Show me test data')
        self.assertEqual(query_history[0]['sql_query'], 'SELECT * FROM test;')
    
    @patch('app.query_database')
    def test_run_query_does_not_add_to_history(self, mock_query_database):
        """Test that run_query endpoint does not add to history."""
        # Mock the database query
        mock_query_database.return_value = {"columns": ["id"], "data": [[1]]}
        
        # Make a request to run_query
        response = self.app.post('/run_query', data={
            'sql_query': 'SELECT 1;'
        })
        
        self.assertEqual(response.status_code, 200)
        
        # Verify no query was added to history
        self.assertEqual(len(query_history), 0)
    
    def test_history_order_preservation(self):
        """Test that history maintains chronological order."""
        queries = [
            ("First query", "SELECT 1;"),
            ("Second query", "SELECT 2;"),
            ("Third query", "SELECT 3;")
        ]
        
        for nl_query, sql_query in queries:
            add_to_history(nl_query, sql_query)
        
        # Verify order in storage (oldest first)
        self.assertEqual(query_history[0]['natural_language_query'], "First query")
        self.assertEqual(query_history[1]['natural_language_query'], "Second query")
        self.assertEqual(query_history[2]['natural_language_query'], "Third query")
        
        # Test API endpoint returns reverse order (newest first)
        response = self.app.get('/query_history')
        data = json.loads(response.data)
        
        self.assertEqual(data['history'][0]['natural_language_query'], "Third query")
        self.assertEqual(data['history'][1]['natural_language_query'], "Second query")
        self.assertEqual(data['history'][2]['natural_language_query'], "First query")
    
    def test_history_with_special_characters(self):
        """Test history handling with special characters and SQL injection attempts."""
        nl_query = "Show me customers with name 'O'Reilly' & \"Smith\""
        sql_query = "SELECT * FROM Customers WHERE name IN ('O''Reilly', \"Smith\");"
        
        add_to_history(nl_query, sql_query)
        
        self.assertEqual(len(query_history), 1)
        self.assertEqual(query_history[0]['natural_language_query'], nl_query)
        self.assertEqual(query_history[0]['sql_query'], sql_query)
        
        # Test API endpoint handles special characters
        response = self.app.get('/query_history')
        data = json.loads(response.data)
        
        self.assertEqual(data['history'][0]['natural_language_query'], nl_query)
        self.assertEqual(data['history'][0]['sql_query'], sql_query)
    
    def test_timestamp_format_consistency(self):
        """Test that timestamps are consistently formatted."""
        add_to_history("Test query", "SELECT 1;")
        
        timestamp = query_history[0]['timestamp']
        
        # Verify timestamp can be parsed back to datetime
        parsed_time = datetime.fromisoformat(timestamp)
        self.assertIsInstance(parsed_time, datetime)
        
        # Verify timestamp is recent (within last minute)
        time_diff = datetime.now() - parsed_time
        self.assertLess(time_diff.total_seconds(), 60)


class TestQueryHistoryIntegration(unittest.TestCase):
    """Integration tests for query history functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.app = app.test_client()
        self.app.testing = True
        query_history.clear()
    
    def tearDown(self):
        """Clean up after each test method."""
        query_history.clear()
    
    @patch('app.SQL_Converter')
    @patch('app.query_database')
    def test_full_workflow_with_history(self, mock_query_database, mock_sql_converter):
        """Test the complete workflow from conversion to execution with history tracking."""
        # Mock the SQL converter
        mock_instance = MagicMock()
        mock_instance.convert_to_sql.return_value = "SELECT * FROM Customers LIMIT 5;"
        mock_sql_converter.return_value = mock_instance
        
        # Mock the database query
        mock_query_database.return_value = {
            "columns": ["CustomerID", "CompanyName"],
            "data": [["ALFKI", "Alfreds Futterkiste"]]
        }
        
        # Step 1: Convert natural language to SQL
        response1 = self.app.post('/convert_to_sql', data={
            'nl_query': 'Show me the first 5 customers'
        })
        self.assertEqual(response1.status_code, 200)
        
        # Verify history was updated
        self.assertEqual(len(query_history), 1)
        
        # Step 2: Execute the query
        response2 = self.app.post('/run_query', data={
            'sql_query': 'SELECT * FROM Customers LIMIT 5;'
        })
        self.assertEqual(response2.status_code, 200)
        
        # Verify history still has only 1 entry (run_query doesn't add to history)
        self.assertEqual(len(query_history), 1)
        
        # Step 3: Check history via API
        response3 = self.app.get('/query_history')
        data = json.loads(response3.data)
        
        self.assertEqual(len(data['history']), 1)
        self.assertEqual(data['history'][0]['natural_language_query'], 'Show me the first 5 customers')
        self.assertEqual(data['history'][0]['sql_query'], 'SELECT * FROM Customers LIMIT 5;')


if __name__ == '__main__':
    unittest.main()
